<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Th<PERSON><PERSON> - <PERSON>on</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .page-header {
            text-align: center;
            margin: 120px 0 50px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .achievements-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0 50px;
        }
        
        .achievement-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            position: relative;
        }
        
        .achievement-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .achievement-image {
            height: 200px;
            overflow: hidden;
        }
        
        .achievement-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s;
        }
        
        .achievement-card:hover .achievement-image img {
            transform: scale(1.05);
        }
        
        .achievement-details {
            padding: 20px;
        }
        
        .achievement-date {
            color: #4285F4;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .achievement-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .achievement-excerpt {
            color: #666;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .read-more {
            color: #4285F4;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }
        
        .read-more i {
            margin-left: 5px;
            transition: transform 0.3s;
        }
        
        .read-more:hover i {
            transform: translateX(3px);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.7);
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .modal.active {
            display: block;
            opacity: 1;
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 80%;
            max-width: 800px;
            box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            transform: translateY(-50px);
            opacity: 0;
            transition: all 0.4s;
        }
        
        .modal.active .modal-content {
            transform: translateY(0);
            opacity: 1;
        }
        
        .close-modal {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.5rem;
            color: #999;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .close-modal:hover {
            color: #333;
        }
        
        .modal-image {
            width: 100%;
            max-height: 400px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 2rem;
            margin-bottom: 15px;
            color: #333;
        }
        
        .modal-date {
            color: #4285F4;
            font-size: 0.9rem;
            margin-bottom: 20px;
        }
        
        .modal-content p {
            margin-bottom: 15px;
            line-height: 1.7;
        }
        
        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                padding: 20px;
                margin: 10% auto;
            }
            
            .modal-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="Vthon Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="classes.html">Lớp Học</a></li>
                    <li><a href="achievements.html" class="active">Thành Tích</a></li>
                    <li><a href="register.html">Đăng Ký</a></li>
                    <li><a href="rankings.html">Bảng Xếp Hạng</a></li>
                    <li><a href="research.html">Nghiên Cứu</a></li>
                    <li><a href="account.html">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Achievements Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Thành Tích Học Viên</h1>
                <p>Tự hào về những thành tựu xuất sắc của học viên Vthon trong lĩnh vực lập trình Python và AI</p>
            </div>
            
            <div class="achievements-container">
                <!-- Achievement 1 -->
                <div class="achievement-card" data-id="1">
                    <div class="achievement-image">
                        <img src="../assets/images/classavatar.png" alt="Cuộc thi lập trình Python">
                    </div>
                    <div class="achievement-details">
                        <div class="achievement-date">15 Tháng 10, 2023</div>
                        <h3 class="achievement-title">Học viên Nguyễn Văn A đạt giải nhất cuộc thi lập trình Python</h3>
                        <p class="achievement-excerpt">Học viên Nguyễn Văn A đến từ lớp Python - A đã xuất sắc giành giải nhất trong cuộc thi lập trình Python cấp thành phố với dự án ứng dụng AI phân loại rác thải.</p>
                        <a href="#" class="read-more">Xem thêm <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <!-- Achievement 2 -->
                <div class="achievement-card" data-id="2">
                    <div class="achievement-image">
                        <img src="../assets/images/classavatar.png" alt="Hackathon AI">
                    </div>
                    <div class="achievement-details">
                        <div class="achievement-date">5 Tháng 9, 2023</div>
                        <h3 class="achievement-title">Nhóm học viên lớp Python - C đạt top 3 Hackathon AI</h3>
                        <p class="achievement-excerpt">Nhóm 4 học viên đến từ lớp Python - C đã xuất sắc lọt vào top 3 cuộc thi Hackathon AI với ứng dụng hỗ trợ người khiếm thị sử dụng công nghệ xử lý hình ảnh.</p>
                        <a href="#" class="read-more">Xem thêm <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <!-- Achievement 3 -->
                <div class="achievement-card" data-id="3">
                    <div class="achievement-image">
                        <img src="../assets/images/classavatar.png" alt="Học bổng du học">
                    </div>
                    <div class="achievement-details">
                        <div class="achievement-date">20 Tháng 8, 2023</div>
                        <h3 class="achievement-title">Học viên Trần Thị B nhận học bổng du học ngành AI</h3>
                        <p class="achievement-excerpt">Học viên Trần Thị B của lớp Python - A đã xuất sắc nhận được học bổng toàn phần du học ngành Trí tuệ nhân tạo tại Đại học Singapore nhờ thành tích học tập nổi bật.</p>
                        <a href="#" class="read-more">Xem thêm <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <!-- Achievement 4 -->
                <div class="achievement-card" data-id="4">
                    <div class="achievement-image">
                        <img src="../assets/images/classavatar.png" alt="Dự án mã nguồn mở">
                    </div>
                    <div class="achievement-details">
                        <div class="achievement-date">10 Tháng 7, 2023</div>
                        <h3 class="achievement-title">Dự án mã nguồn mở của học viên lớp Python đạt 1000 sao trên GitHub</h3>
                        <p class="achievement-excerpt">Dự án mã nguồn mở phát triển thư viện xử lý ảnh bằng Python của nhóm học viên lớp Python - A đã đạt mốc 1000 sao trên GitHub, thu hút sự quan tâm của cộng đồng lập trình viên.</p>
                        <a href="#" class="read-more">Xem thêm <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <!-- Achievement 5 -->
                <div class="achievement-card" data-id="5">
                    <div class="achievement-image">
                        <img src="../assets/images/classavatar.png" alt="Tuyển dụng">
                    </div>
                    <div class="achievement-details">
                        <div class="achievement-date">1 Tháng 6, 2023</div>
                        <h3 class="achievement-title">5 học viên được tuyển dụng vào các công ty công nghệ hàng đầu</h3>
                        <p class="achievement-excerpt">5 học viên xuất sắc từ các lớp Python đã được tuyển dụng vào các vị trí lập trình viên Python tại các công ty công nghệ hàng đầu với mức lương khởi điểm hấp dẫn.</p>
                        <a href="#" class="read-more">Xem thêm <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <!-- Achievement 6 -->
                <div class="achievement-card" data-id="6">
                    <div class="achievement-image">
                        <img src="../assets/images/classavatar.png" alt="Ứng dụng AI">
                    </div>
                    <div class="achievement-details">
                        <div class="achievement-date">15 Tháng 5, 2023</div>
                        <h3 class="achievement-title">Ứng dụng AI của học viên đạt giải thưởng sáng tạo</h3>
                        <p class="achievement-excerpt">Ứng dụng AI dự đoán và cảnh báo thiên tai của học viên Lê Văn C từ lớp Python - C đã đạt giải thưởng sáng tạo trong cuộc thi công nghệ vì cộng đồng.</p>
                        <a href="#" class="read-more">Xem thêm <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Modal for Achievement Details -->
    <div class="modal" id="achievementModal">
        <div class="modal-content">
            <span class="close-modal"><i class="fas fa-times"></i></span>
            <img src="" alt="" class="modal-image">
            <div class="modal-date"></div>
            <h2 class="modal-title"></h2>
            <div class="modal-body"></div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; Kiến thức có đầu nhưng không có cuối - Vthon. Tất cả quyền được bảo lưu.</p>
        </div>
    </footer>

    <script src="../assets/js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const achievementCards = document.querySelectorAll('.achievement-card');
            const modal = document.getElementById('achievementModal');
            const modalImage = modal.querySelector('.modal-image');
            const modalDate = modal.querySelector('.modal-date');
            const modalTitle = modal.querySelector('.modal-title');
            const modalBody = modal.querySelector('.modal-body');
            const closeModal = modal.querySelector('.close-modal');
            
            // Achievement details content
            const achievementDetails = {
                1: {
                    title: "Học viên Nguyễn Văn A đạt giải nhất cuộc thi lập trình Python",
                    date: "15 Tháng 10, 2023",
                    image: "../assets/images/classavatar.png",
                    content: `
                        <p>Học viên Nguyễn Văn A đến từ lớp Python - A đã xuất sắc giành giải nhất trong cuộc thi lập trình Python cấp thành phố với dự án ứng dụng AI phân loại rác thải.</p>
                        
                        <p>Cuộc thi diễn ra trong hai ngày 14-15/10/2023 với sự tham gia của hơn 100 thí sinh đến từ các trường đại học và trung tâm đào tạo trong thành phố. Nguyễn Văn A đã vượt qua nhiều đối thủ nặng ký để giành chiến thắng chung cuộc.</p>
                        
                        <p>Dự án của Nguyễn Văn A sử dụng kỹ thuật deep learning để nhận diện và phân loại rác thải qua hình ảnh, giúp tự động hóa quá trình phân loại rác tại các nhà máy xử lý. Ứng dụng đạt độ chính xác lên tới 95% và có khả năng xử lý hình ảnh trong thời gian thực.</p>
                        
                        <p>Ban giám khảo đánh giá cao tính ứng dụng thực tiễn và khả năng mở rộng của dự án. Phần thưởng cho giải nhất bao gồm 20 triệu đồng tiền mặt và cơ hội thực tập tại một công ty công nghệ hàng đầu.</p>
                        
                        <p>Nguyễn Văn A chia sẻ: "Em rất bất ngờ và hạnh phúc khi nhận được giải thưởng này. Em xin gửi lời cảm ơn đến thầy cô tại Vthon đã hướng dẫn và truyền cảm hứng cho em trong suốt quá trình học tập. Kiến thức và kỹ năng mà em học được tại đây đã giúp em rất nhiều trong việc phát triển dự án này."</p>
                        
                        <p>Đây là thành tích đáng tự hào không chỉ cho cá nhân Nguyễn Văn A mà còn cho toàn bộ lớp học Python - A và trung tâm Vthon.</p>
                    `
                },
                2: {
                    title: "Nhóm học viên lớp Python - C đạt top 3 Hackathon AI",
                    date: "5 Tháng 9, 2023",
                    image: "../assets/images/classavatar.png",
                    content: `
                        <p>Nhóm 4 học viên đến từ lớp Python - C đã xuất sắc lọt vào top 3 cuộc thi Hackathon AI với ứng dụng hỗ trợ người khiếm thị sử dụng công nghệ xử lý hình ảnh.</p>
                        
                        <p>Hackathon AI 2023 là sự kiện quy tụ hơn 50 đội thi đến từ khắp cả nước, diễn ra trong 48 giờ liên tục từ ngày 3 đến ngày 5 tháng 9. Nhóm học viên gồm Trần Minh D, Lê Thị E, Phạm Văn F và Nguyễn Thị G đã cùng nhau phát triển ứng dụng có tên "EyesForAll" - một giải pháp sử dụng AI để mô tả môi trường xung quanh cho người khiếm thị thông qua giọng nói.</p>
                        
                        <p>Ứng dụng này sử dụng các mô hình computer vision kết hợp với xử lý ngôn ngữ tự nhiên để nhận diện đối tượng, đọc văn bản và mô tả chi tiết không gian xung quanh người dùng. Đặc biệt, ứng dụng có thể hoạt động offline và được tối ưu hóa để chạy trên các thiết bị di động có cấu hình trung bình.</p>
                        
                        <p>Ban tổ chức đánh giá cao tính nhân văn và khả năng ứng dụng thực tế của sản phẩm. Nhóm học viên đã nhận được giải thưởng trị giá 30 triệu đồng và cơ hội tham gia chương trình ươm tạo startup công nghệ.</p>
                        
                        <p>Trần Minh D, trưởng nhóm chia sẻ: "Chúng em đã làm việc không ngừng nghỉ trong suốt 48 giờ để hoàn thiện ứng dụng. Kiến thức về Python và AI mà chúng em học được tại Vthon đã giúp chúng em rất nhiều trong quá trình phát triển sản phẩm. Chúng em dự định sẽ tiếp tục phát triển ứng dụng này và mong muốn có thể đưa nó đến với nhiều người khiếm thị hơn trong tương lai."</p>
                    `
                },
                3: {
                    title: "Học viên Trần Thị B nhận học bổng du học ngành AI",
                    date: "20 Tháng 8, 2023",
                    image: "../assets/images/classavatar.png",
                    content: `
                        <p>Học viên Trần Thị B của lớp Python - A đã xuất sắc nhận được học bổng toàn phần du học ngành Trí tuệ nhân tạo tại Đại học Singapore nhờ thành tích học tập nổi bật.</p>
                        
                        <p>Trần Thị B, 22 tuổi, đã tham gia khóa học Python và AI tại Vthon trong hai năm qua. Cô đã thể hiện khả năng xuất sắc và đam mê với lĩnh vực AI, đặc biệt là trong các dự án nghiên cứu về học máy và xử lý ngôn ngữ tự nhiên.</p>
                        
                        <p>Học bổng toàn phần mà Trần Thị B nhận được có giá trị hơn 1 tỷ đồng, bao gồm toàn bộ học phí và chi phí sinh hoạt cho chương trình Thạc sĩ Khoa học Dữ liệu và Trí tuệ nhân tạo tại một trong những trường đại học hàng đầu châu Á.</p>
                        
                        <p>Quá trình xét tuyển học bổng diễn ra vô cùng cạnh tranh với hơn 500 ứng viên từ nhiều quốc gia. Trần Thị B đã gây ấn tượng với hội đồng tuyển sinh thông qua hồ sơ học thuật xuất sắc, kinh nghiệm dự án thực tế và bài luận cá nhân đầy cảm hứng.</p>
                        
                        <p>Trong bài phỏng vấn với báo chí, Trần Thị B chia sẻ: "Em rất biết ơn các thầy cô tại Vthon đã giúp em xây dựng nền tảng kiến thức vững chắc về Python và AI. Những dự án thực tế mà em được tham gia trong quá trình học tập tại đây đã giúp em rất nhiều trong việc chuẩn bị hồ sơ du học. Em hy vọng sẽ tiếp tục phát triển và đóng góp cho cộng đồng AI Việt Nam trong tương lai."</p>
                        
                        <p>Đây là học viên thứ ba của Vthon nhận được học bổng du học quốc tế trong năm nay, minh chứng cho chất lượng đào tạo và sự hỗ trợ toàn diện mà trung tâm dành cho học viên.</p>
                    `
                },
                4: {
                    title: "Dự án mã nguồn mở của học viên lớp Python đạt 1000 sao trên GitHub",
                    date: "10 Tháng 7, 2023",
                    image: "../assets/images/classavatar.png",
                    content: `
                        <p>Dự án mã nguồn mở phát triển thư viện xử lý ảnh bằng Python của nhóm học viên lớp Python - A đã đạt mốc 1000 sao trên GitHub, thu hút sự quan tâm của cộng đồng lập trình viên.</p>
                        
                        <p>Thư viện có tên "PyImagePro" được phát triển bởi nhóm 6 học viên lớp Python - A, dưới sự hướng dẫn của giảng viên. Dự án này cung cấp các công cụ và thuật toán xử lý ảnh hiệu quả, tối ưu hóa cho các ứng dụng thực tế và dễ dàng tích hợp với các framework AI phổ biến.</p>
                        
                        <p>PyImagePro cung cấp nhiều tính năng nổi bật như: cải thiện chất lượng ảnh tự động, phát hiện và nhận dạng đối tượng, phân đoạn ảnh, tạo hiệu ứng nghệ thuật và nhiều thuật toán xử lý ảnh tiên tiến khác. Điểm đặc biệt của thư viện này là khả năng tối ưu hóa hiệu suất và dễ dàng sử dụng ngay cả với người mới bắt đầu.</p>
                        
                        <p>Kể từ khi ra mắt cách đây 6 tháng, dự án đã nhận được sự đóng góp từ hơn 50 lập trình viên trên toàn thế giới và được sử dụng trong nhiều ứng dụng thương mại và nghiên cứu. Đặc biệt, một công ty khởi nghiệp trong lĩnh vực AI đã quyết định sử dụng PyImagePro làm nền tảng cho sản phẩm của họ.</p>
                        
                        <p>Hoàng Minh H, một trong những thành viên chính của dự án chia sẻ: "Chúng em bắt đầu dự án này như một bài tập nhóm, nhưng sau đó nhận ra tiềm năng và quyết định phát triển nó thành một thư viện đầy đủ. Chúng em rất vui khi thấy cộng đồng đón nhận và sử dụng sản phẩm của mình. Kiến thức về Python, AI và phương pháp phát triển phần mềm mà chúng em học được tại Vthon đã giúp chúng em rất nhiều trong quá trình này."</p>
                        
                        <p>Nhóm học viên dự định sẽ tiếp tục phát triển thêm các tính năng mới cho PyImagePro và tổ chức các workshops hướng dẫn sử dụng thư viện này trong các dự án thực tế.</p>
                    `
                },
                5: {
                    title: "5 học viên được tuyển dụng vào các công ty công nghệ hàng đầu",
                    date: "1 Tháng 6, 2023",
                    image: "../assets/images/classavatar.png",
                    content: `
                        <p>5 học viên xuất sắc từ các lớp Python đã được tuyển dụng vào các vị trí lập trình viên Python tại các công ty công nghệ hàng đầu với mức lương khởi điểm hấp dẫn.</p>
                        
                        <p>Các học viên bao gồm Nguyễn Văn K từ lớp Python - A, Trần Thị L và Phạm Văn M từ lớp Python - C, và Lê Thị N và Hoàng Văn P từ khóa học trước. Họ đã vượt qua các vòng phỏng vấn đầy cạnh tranh để giành được vị trí tại các công ty công nghệ như FPT Software, VNG, Tiki, Momo và một công ty phần mềm đa quốc gia.</p>
                        
                        <p>Đáng chú ý, tất cả các học viên này đều nhận được mức lương khởi điểm từ 15 đến 25 triệu đồng/tháng, cao hơn nhiều so với mức trung bình của sinh viên mới ra trường. Điều này phản ánh chất lượng đào tạo và giá trị thực tế của các kỹ năng Python và AI mà họ đã tiếp thu được tại Vthon.</p>
                        
                        <p>Trong quá trình phỏng vấn, các học viên đã thể hiện không chỉ kiến thức lý thuyết vững chắc mà còn có kinh nghiệm thực tế với các dự án thực tế và portfolio ấn tượng. Đặc biệt, khả năng giải quyết vấn đề và tư duy logic của họ được các nhà tuyển dụng đánh giá rất cao.</p>
                        
                        <p>Trần Thị L, học viên đã được tuyển dụng vào VNG chia sẻ: "Em rất biết ơn thầy cô tại Vthon đã không chỉ dạy em kiến thức chuyên môn mà còn hướng dẫn em cách xây dựng portfolio và chuẩn bị cho các buổi phỏng vấn. Các dự án thực tế mà em được tham gia trong quá trình học đã giúp em rất nhiều trong việc thể hiện năng lực của mình với nhà tuyển dụng."</p>
                        
                        <p>Đại diện trung tâm Vthon cho biết: "Chúng tôi rất tự hào về thành tích của các học viên. Việc họ được các công ty hàng đầu tuyển dụng là minh chứng cho phương pháp đào tạo thực tiễn và toàn diện của chúng tôi. Chúng tôi không chỉ dạy kỹ thuật mà còn giúp học viên phát triển tư duy, kỹ năng mềm và chuẩn bị sẵn sàng cho môi trường làm việc thực tế."</p>
                    `
                },
                6: {
                    title: "Ứng dụng AI của học viên đạt giải thưởng sáng tạo",
                    date: "15 Tháng 5, 2023",
                    image: "../assets/images/classavatar.png",
                    content: `
                        <p>Ứng dụng AI dự đoán và cảnh báo thiên tai của học viên Lê Văn C từ lớp Python - C đã đạt giải thưởng sáng tạo trong cuộc thi công nghệ vì cộng đồng.</p>
                        
                        <p>Cuộc thi "Công nghệ vì cộng đồng" là sự kiện thường niên nhằm tìm kiếm và hỗ trợ các giải pháp công nghệ có tác động tích cực đến xã hội. Năm nay, cuộc thi nhận được hơn 200 dự án từ các cá nhân và nhóm trên khắp cả nước.</p>
                        
                        <p>Ứng dụng "DisasterPredict" của Lê Văn C là một hệ thống sử dụng AI và dữ liệu thời tiết để dự đoán và cảnh báo sớm các hiện tượng thiên tai như lũ lụt, sạt lở đất và bão. Ứng dụng phân tích dữ liệu từ nhiều nguồn khác nhau, bao gồm cả vệ tinh và các trạm quan trắc, để đưa ra cảnh báo chính xác với thời gian dự báo trước từ 24 đến 72 giờ.</p>
                        
                        <p>Điểm nổi bật của ứng dụng là khả năng hoạt động ngay cả trong điều kiện kết nối internet không ổn định và tối ưu hóa cho các thiết bị di động phổ thông, giúp nó trở nên hữu ích cho người dân ở các vùng sâu, vùng xa và vùng thường xuyên chịu ảnh hưởng của thiên tai.</p>
                        
                        <p>Ban giám khảo đánh giá cao tính thực tiễn và tác động xã hội của ứng dụng. Lê Văn C đã nhận được giải thưởng trị giá 50 triệu đồng và cơ hội tham gia chương trình đào tạo đặc biệt về phát triển sản phẩm và khởi nghiệp.</p>
                        
                        <p>Lê Văn C chia sẻ: "Em đến từ một vùng thường xuyên bị lũ lụt, nên em hiểu rõ tầm quan trọng của việc cảnh báo sớm. Kiến thức về Python, AI và xử lý dữ liệu lớn mà em học được tại Vthon đã giúp em hiện thực hóa ý tưởng này. Em hy vọng ứng dụng của mình có thể giúp giảm thiểu thiệt hại do thiên tai gây ra cho cộng đồng."</p>
                        
                        <p>Sau cuộc thi, Lê Văn C đã nhận được sự quan tâm từ một số tổ chức phi chính phủ về môi trường và phòng chống thiên tai, mở ra cơ hội phát triển và triển khai ứng dụng trong thực tế.</p>
                    `
                }
            };
            
            // Open modal with achievement details
            achievementCards.forEach(card => {
                card.addEventListener('click', function() {
                    const achievementId = this.dataset.id;
                    const details = achievementDetails[achievementId];
                    
                    modalImage.src = details.image;
                    modalImage.alt = details.title;
                    modalDate.textContent = details.date;
                    modalTitle.textContent = details.title;
                    modalBody.innerHTML = details.content;
                    
                    modal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            });
            
            // Close modal
            closeModal.addEventListener('click', function() {
                modal.classList.remove('active');
                document.body.style.overflow = 'auto';
            });
            
            // Close modal when clicking outside content
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.classList.remove('active');
                    document.body.style.overflow = 'auto';
                }
            });
        });
    </script>
</body>
</html> 