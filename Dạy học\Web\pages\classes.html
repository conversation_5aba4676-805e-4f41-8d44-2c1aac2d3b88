<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lớp <PERSON> - <PERSON>thon</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .page-header {
            text-align: center;
            margin: 120px 0 50px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .classes-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            padding: 20px 0 50px;
        }
        
        .class-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 350px;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .class-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .class-image {
            height: 180px;
            overflow: hidden;
        }
        
        .class-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .class-details {
            padding: 20px;
        }
        
        .class-name {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .class-schedule {
            margin-bottom: 15px;
        }
        
        .schedule-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            color: #555;
        }
        
        .schedule-item i {
            color: #4285F4;
            margin-right: 10px;
            font-size: 1.1rem;
        }
        
        .students-section {
            margin-top: 20px;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        
        .students-count {
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }
        
        .students-avatars {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .student-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .student-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .empty-class {
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
        }
        
        .empty-notice {
            color: #6c757d;
            font-style: italic;
            margin-top: 5px;
        }
        
        .join-class {
            display: inline-block;
            background-color: #4285F4;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            margin-top: 15px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        
        .join-class:hover {
            background-color: #3367D6;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="Vthon Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="classes.html" class="active">Lớp Học</a></li>
                    <li><a href="achievements.html">Thành Tích</a></li>
                    <li><a href="register.html">Đăng Ký</a></li>
                    <li><a href="rankings.html">Bảng Xếp Hạng</a></li>
                    <li><a href="research.html">Nghiên Cứu</a></li>
                    <li><a href="account.html">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Classes Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Các Lớp Học Hiện Tại</h1>
                <p>Khám phá và tham gia các lớp học Python và AI cùng Vthon</p>
            </div>
            
            <div class="classes-container">
                <!-- Python A Class -->
                <div class="class-card">
                    <div class="class-image">
                        <img src="../assets/images/classavatar.png" alt="Python - A">
                    </div>
                    <div class="class-details">
                        <h3 class="class-name">Python - A</h3>
                        <div class="class-schedule">
                            <div class="schedule-item">
                                <i class="fas fa-calendar-alt"></i>
                                <span>Thứ 7 - Chủ Nhật</span>
                            </div>
                            <div class="schedule-item">
                                <i class="fas fa-clock"></i>
                                <span>19:30 - 21:00</span>
                            </div>
                        </div>
                        <div class="students-section">
                            <div class="students-count">Số học viên: 6</div>
                            <div class="students-avatars">
                                <div class="student-avatar">
                                    <img src="../assets/images/user.png" alt="Học viên">
                                </div>
                                <div class="student-avatar">
                                    <img src="../assets/images/user.png" alt="Học viên">
                                </div>
                                <div class="student-avatar">
                                    <img src="../assets/images/user.png" alt="Học viên">
                                </div>
                                <div class="student-avatar">
                                    <img src="../assets/images/user.png" alt="Học viên">
                                </div>
                                <div class="student-avatar">
                                    <img src="../assets/images/user.png" alt="Học viên">
                                </div>
                                <div class="student-avatar">
                                    <img src="../assets/images/user.png" alt="Học viên">
                                </div>
                            </div>
                        </div>
                        <a href="#" class="join-class">Tham gia lớp học</a>
                    </div>
                </div>
                
                <!-- Python C Class -->
                <div class="class-card">
                    <div class="class-image">
                        <img src="../assets/images/classavatar.png" alt="Python - C">
                    </div>
                    <div class="class-details">
                        <h3 class="class-name">Python - C</h3>
                        <div class="class-schedule">
                            <div class="schedule-item">
                                <i class="fas fa-calendar-alt"></i>
                                <span>Thứ 3 - Thứ 5</span>
                            </div>
                            <div class="schedule-item">
                                <i class="fas fa-clock"></i>
                                <span>19:30 - 21:00</span>
                            </div>
                        </div>
                        <div class="students-section">
                            <div class="students-count">Số học viên: 6</div>
                            <div class="students-avatars">
                                <div class="student-avatar">
                                    <img src="../assets/images/user.png" alt="Học viên">
                                </div>
                                <div class="student-avatar">
                                    <img src="../assets/images/user.png" alt="Học viên">
                                </div>
                                <div class="student-avatar">
                                    <img src="../assets/images/user.png" alt="Học viên">
                                </div>
                                <div class="student-avatar">
                                    <img src="../assets/images/user.png" alt="Học viên">
                                </div>
                                <div class="student-avatar">
                                    <img src="../assets/images/user.png" alt="Học viên">
                                </div>
                                <div class="student-avatar">
                                    <img src="../assets/images/user.png" alt="Học viên">
                                </div>
                            </div>
                        </div>
                        <a href="#" class="join-class">Tham gia lớp học</a>
                    </div>
                </div>
                
                <!-- Python B Class (Empty) -->
                <div class="class-card empty-class">
                    <div class="class-image">
                        <img src="../assets/images/classavatar.png" alt="Python - B">
                    </div>
                    <div class="class-details">
                        <h3 class="class-name">Python - B</h3>
                        <div class="class-schedule">
                            <div class="schedule-item">
                                <i class="fas fa-calendar-alt"></i>
                                <span>Thứ 2 - Thứ 4</span>
                            </div>
                            <div class="schedule-item">
                                <i class="fas fa-clock"></i>
                                <span>19:30 - 21:00</span>
                            </div>
                        </div>
                        <div class="students-section">
                            <div class="students-count">Số học viên: 0</div>
                            <p class="empty-notice">Lớp học chưa có học viên</p>
                        </div>
                        <a href="#" class="join-class">Tham gia lớp học</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; Kiến thức có đầu nhưng không có cuối - Vthon. Tất cả quyền được bảo lưu.</p>
        </div>
    </footer>

    <script src="../assets/js/script.js"></script>
</body>
</html> 