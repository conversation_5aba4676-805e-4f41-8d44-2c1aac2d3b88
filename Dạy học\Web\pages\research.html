<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .page-header {
            text-align: center;
            margin: 120px 0 50px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .research-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            padding: 20px 0 50px;
        }
        
        .research-card {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        
        .research-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .research-image {
            height: 200px;
            overflow: hidden;
            position: relative;
        }
        
        .research-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }
        
        .research-card:hover .research-image img {
            transform: scale(1.05);
        }
        
        .research-category {
            position: absolute;
            top: 15px;
            left: 15px;
            background: linear-gradient(45deg, #4285F4, #ff7aa8);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .research-content {
            padding: 25px;
        }
        
        .research-title {
            font-size: 1.4rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
            line-height: 1.3;
        }
        
        .research-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
            color: #666;
            font-size: 0.9rem;
        }
        
        .research-meta i {
            color: #4285F4;
        }
        
        .research-excerpt {
            color: #555;
            line-height: 1.6;
            margin-bottom: 20px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .read-more-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(45deg, #4285F4, #ff7aa8);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.3s;
        }
        
        .read-more-btn:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.3);
        }
        
        .research-tags {
            margin-top: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .research-tag {
            background: #f8f9fa;
            color: #666;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8rem;
            border: 1px solid #e9ecef;
        }
        
        /* Research Detail Modal */
        .research-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }
        
        .research-modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }
        
        .modal-header {
            position: relative;
            height: 250px;
            overflow: hidden;
        }
        
        .modal-header img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .modal-close {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .modal-close:hover {
            background: rgba(0, 0, 0, 0.7);
        }
        
        .modal-body {
            padding: 30px;
        }
        
        .modal-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 15px;
            font-weight: 700;
        }
        
        .modal-meta {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 25px;
            color: #666;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .modal-content-text {
            line-height: 1.8;
            color: #444;
            font-size: 1.1rem;
        }
        
        .modal-content-text h3 {
            color: #4285F4;
            margin: 25px 0 15px;
            font-size: 1.3rem;
        }
        
        .modal-content-text p {
            margin-bottom: 15px;
        }
        
        .modal-content-text ul, .modal-content-text ol {
            margin: 15px 0;
            padding-left: 25px;
        }
        
        .modal-content-text li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="Vthon Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="classes.html">Lớp Học</a></li>
                    <li><a href="achievements.html">Thành Tích</a></li>
                    <li><a href="register.html">Đăng Ký</a></li>
                    <li><a href="rankings.html">Bảng Xếp Hạng</a></li>
                    <li><a href="research.html" class="active">Nghiên Cứu</a></li>
                    <li><a href="account.html">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Research Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Nghiên Cứu & Bài Báo</h1>
                <p>Khám phá những nghiên cứu mới nhất về Python, AI và Machine Learning từ cộng đồng Vthon</p>
            </div>
            
            <div class="research-container">
                <!-- Research Article 1 -->
                <div class="research-card" onclick="openResearchModal('research1')">
                    <div class="research-image">
                        <img src="../assets/images/background.jpg" alt="Ứng dụng AI trong giáo dục">
                        <div class="research-category">AI & Giáo dục</div>
                    </div>
                    <div class="research-content">
                        <h3 class="research-title">Ứng dụng Trí tuệ Nhân tạo trong Giáo dục: Cơ hội và Thách thức</h3>
                        <div class="research-meta">
                            <span><i class="fas fa-calendar"></i> 15/11/2024</span>
                            <span><i class="fas fa-user"></i> Lê Quang Vinh</span>
                            <span><i class="fas fa-clock"></i> 8 phút đọc</span>
                        </div>
                        <p class="research-excerpt">
                            Nghiên cứu về việc tích hợp AI vào hệ thống giáo dục hiện đại, từ chatbot hỗ trợ học tập đến hệ thống đánh giá tự động...
                        </p>
                        <div class="research-tags">
                            <span class="research-tag">AI</span>
                            <span class="research-tag">Giáo dục</span>
                            <span class="research-tag">Machine Learning</span>
                        </div>
                        <a href="#" class="read-more-btn">
                            Đọc thêm <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Research Article 2 -->
                <div class="research-card" onclick="openResearchModal('research2')">
                    <div class="research-image">
                        <img src="../assets/images/classavatar.png" alt="Python trong Data Science">
                        <div class="research-category">Data Science</div>
                    </div>
                    <div class="research-content">
                        <h3 class="research-title">Python và Data Science: Hành trình từ Cơ bản đến Nâng cao</h3>
                        <div class="research-meta">
                            <span><i class="fas fa-calendar"></i> 10/11/2024</span>
                            <span><i class="fas fa-user"></i> Lê Quang Vinh</span>
                            <span><i class="fas fa-clock"></i> 12 phút đọc</span>
                        </div>
                        <p class="research-excerpt">
                            Khám phá sức mạnh của Python trong lĩnh vực Data Science, từ xử lý dữ liệu với Pandas đến machine learning với Scikit-learn...
                        </p>
                        <div class="research-tags">
                            <span class="research-tag">Python</span>
                            <span class="research-tag">Data Science</span>
                            <span class="research-tag">Pandas</span>
                        </div>
                        <a href="#" class="read-more-btn">
                            Đọc thêm <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Research Article 3 -->
                <div class="research-card" onclick="openResearchModal('research3')">
                    <div class="research-image">
                        <img src="../assets/images/logo.jpg" alt="Tương lai của AI">
                        <div class="research-category">Tương lai AI</div>
                    </div>
                    <div class="research-content">
                        <h3 class="research-title">Tương lai của Trí tuệ Nhân tạo: Dự đoán và Xu hướng 2025</h3>
                        <div class="research-meta">
                            <span><i class="fas fa-calendar"></i> 05/11/2024</span>
                            <span><i class="fas fa-user"></i> Lê Quang Vinh</span>
                            <span><i class="fas fa-clock"></i> 10 phút đọc</span>
                        </div>
                        <p class="research-excerpt">
                            Phân tích các xu hướng phát triển của AI trong năm 2025, từ Large Language Models đến AI tạo sinh và ứng dụng thực tế...
                        </p>
                        <div class="research-tags">
                            <span class="research-tag">AI</span>
                            <span class="research-tag">Tương lai</span>
                            <span class="research-tag">LLM</span>
                        </div>
                        <a href="#" class="read-more-btn">
                            Đọc thêm <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; Kiến thức có đầu nhưng không có cuối - Vthon. Tất cả quyền được bảo lưu.</p>
        </div>
    </footer>

    <!-- Research Detail Modal -->
    <div id="researchModal" class="research-modal">
        <div class="research-modal-content">
            <div class="modal-header">
                <img id="modalImage" src="" alt="">
                <button class="modal-close" onclick="closeResearchModal()">&times;</button>
            </div>
            <div class="modal-body">
                <h2 id="modalTitle" class="modal-title"></h2>
                <div id="modalMeta" class="modal-meta"></div>
                <div id="modalContent" class="modal-content-text"></div>
            </div>
        </div>
    </div>

    <script>
        // Research data
        const researchData = {
            research1: {
                title: "Ứng dụng Trí tuệ Nhân tạo trong Giáo dục: Cơ hội và Thách thức",
                image: "../assets/images/background.jpg",
                meta: {
                    date: "15/11/2024",
                    author: "Lê Quang Vinh",
                    readTime: "8 phút đọc"
                },
                content: `
                    <h3>Giới thiệu</h3>
                    <p>Trí tuệ nhân tạo (AI) đang tạo ra những thay đổi mạnh mẽ trong nhiều lĩnh vực, và giáo dục không phải là ngoại lệ. Việc tích hợp AI vào hệ thống giáo dục mở ra những cơ hội to lớn nhưng cũng đặt ra nhiều thách thức cần được giải quyết.</p>

                    <h3>Cơ hội từ AI trong Giáo dục</h3>
                    <p><strong>1. Cá nhân hóa học tập:</strong> AI có thể phân tích dữ liệu học tập của từng học sinh để tạo ra lộ trình học tập phù hợp với khả năng và tốc độ học của mỗi người.</p>

                    <p><strong>2. Hỗ trợ giảng dạy:</strong> Chatbot AI có thể trả lời câu hỏi của học sinh 24/7, giúp giáo viên tiết kiệm thời gian và tập trung vào những nhiệm vụ quan trọng hơn.</p>

                    <p><strong>3. Đánh giá tự động:</strong> Hệ thống AI có thể chấm điểm bài tập, bài kiểm tra một cách nhanh chóng và chính xác, đồng thời cung cấp phản hồi chi tiết.</p>

                    <h3>Thách thức cần vượt qua</h3>
                    <p><strong>1. Vấn đề đạo đức:</strong> Việc sử dụng AI trong giáo dục cần đảm bảo tính công bằng và không tạo ra sự phân biệt đối xử.</p>

                    <p><strong>2. Bảo mật dữ liệu:</strong> Thông tin học tập của học sinh cần được bảo vệ một cách tối đa.</p>

                    <p><strong>3. Đào tạo giáo viên:</strong> Giáo viên cần được đào tạo để sử dụng hiệu quả các công cụ AI.</p>

                    <h3>Kết luận</h3>
                    <p>AI trong giáo dục là xu hướng không thể tránh khỏi. Việc chuẩn bị tốt và áp dụng một cách có trách nhiệm sẽ giúp chúng ta tận dụng tối đa những lợi ích mà AI mang lại cho ngành giáo dục.</p>
                `
            },
            research2: {
                title: "Python và Data Science: Hành trình từ Cơ bản đến Nâng cao",
                image: "../assets/images/classavatar.png",
                meta: {
                    date: "10/11/2024",
                    author: "Lê Quang Vinh",
                    readTime: "12 phút đọc"
                },
                content: `
                    <h3>Tại sao Python lại phù hợp cho Data Science?</h3>
                    <p>Python đã trở thành ngôn ngữ lập trình hàng đầu trong lĩnh vực Data Science nhờ vào tính đơn giản, dễ học và hệ sinh thái thư viện phong phú.</p>

                    <h3>Các thư viện quan trọng</h3>
                    <p><strong>1. NumPy:</strong> Thư viện cơ bản cho tính toán khoa học, cung cấp mảng đa chiều hiệu quả.</p>

                    <p><strong>2. Pandas:</strong> Công cụ mạnh mẽ cho việc xử lý và phân tích dữ liệu có cấu trúc.</p>

                    <p><strong>3. Matplotlib & Seaborn:</strong> Tạo biểu đồ và trực quan hóa dữ liệu chuyên nghiệp.</p>

                    <p><strong>4. Scikit-learn:</strong> Thư viện machine learning toàn diện với nhiều thuật toán được tối ưu.</p>

                    <h3>Quy trình Data Science với Python</h3>
                    <ol>
                        <li><strong>Thu thập dữ liệu:</strong> Sử dụng requests, BeautifulSoup để crawl dữ liệu</li>
                        <li><strong>Làm sạch dữ liệu:</strong> Pandas giúp xử lý missing values, outliers</li>
                        <li><strong>Khám phá dữ liệu:</strong> Phân tích thống kê mô tả và trực quan hóa</li>
                        <li><strong>Xây dựng mô hình:</strong> Áp dụng các thuật toán machine learning</li>
                        <li><strong>Đánh giá và triển khai:</strong> Kiểm tra hiệu suất và deploy mô hình</li>
                    </ol>

                    <h3>Lời khuyên cho người mới bắt đầu</h3>
                    <p>Hãy bắt đầu với những project nhỏ, thực hành thường xuyên và tham gia cộng đồng để học hỏi kinh nghiệm từ những người đi trước.</p>
                `
            },
            research3: {
                title: "Tương lai của Trí tuệ Nhân tạo: Dự đoán và Xu hướng 2025",
                image: "../assets/images/logo.jpg",
                meta: {
                    date: "05/11/2024",
                    author: "Lê Quang Vinh",
                    readTime: "10 phút đọc"
                },
                content: `
                    <h3>Xu hướng AI năm 2025</h3>
                    <p>Năm 2025 hứa hẹn sẽ là một năm bùng nổ của AI với nhiều đột phá công nghệ quan trọng.</p>

                    <h3>1. Large Language Models (LLMs) tiến bộ hơn</h3>
                    <p>Các mô hình ngôn ngữ lớn sẽ trở nên thông minh hơn, có khả năng reasoning tốt hơn và tiêu thụ ít tài nguyên hơn.</p>

                    <h3>2. AI Multimodal</h3>
                    <p>AI sẽ có khả năng xử lý đồng thời nhiều loại dữ liệu: văn bản, hình ảnh, âm thanh, video một cách tự nhiên.</p>

                    <h3>3. AI Edge Computing</h3>
                    <p>AI sẽ được tích hợp trực tiếp vào các thiết bị di động và IoT, giảm độ trễ và tăng tính bảo mật.</p>

                    <h3>4. AI trong Healthcare</h3>
                    <p>Chẩn đoán bệnh tự động, phát triển thuốc bằng AI và y học cá nhân hóa sẽ trở thành hiện thực.</p>

                    <h3>5. Autonomous Systems</h3>
                    <p>Xe tự lái, robot gia đình và hệ thống tự động trong sản xuất sẽ được triển khai rộng rãi.</p>

                    <h3>Thách thức và Cơ hội</h3>
                    <p>Bên cạnh những tiến bộ, chúng ta cũng cần đối mặt với các vấn đề về đạo đức AI, việc làm và quyền riêng tư.</p>

                    <h3>Kết luận</h3>
                    <p>2025 sẽ là năm mà AI thực sự trở thành một phần không thể thiếu trong cuộc sống hàng ngày của chúng ta.</p>
                `
            }
        };

        function openResearchModal(researchId) {
            const research = researchData[researchId];
            if (!research) return;

            document.getElementById('modalTitle').textContent = research.title;
            document.getElementById('modalImage').src = research.image;
            document.getElementById('modalMeta').innerHTML = `
                <span><i class="fas fa-calendar"></i> ${research.meta.date}</span>
                <span><i class="fas fa-user"></i> ${research.meta.author}</span>
                <span><i class="fas fa-clock"></i> ${research.meta.readTime}</span>
            `;
            document.getElementById('modalContent').innerHTML = research.content;
            document.getElementById('researchModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeResearchModal() {
            document.getElementById('researchModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('researchModal');
            if (event.target === modal) {
                closeResearchModal();
            }
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeResearchModal();
            }
        });
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>
