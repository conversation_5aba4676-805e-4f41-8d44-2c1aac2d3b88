document.addEventListener('DOMContentLoaded', function() {
    // Add animation classes to elements when they enter the viewport
    const animateOnScroll = function() {
        const elements = document.querySelectorAll('.card, .page-header, .form-header, .rules-header, .contact-info');
        
        elements.forEach(element => {
            const elementPosition = element.getBoundingClientRect().top;
            const screenPosition = window.innerHeight / 1.2;
            
            if (elementPosition < screenPosition) {
                element.classList.add('animated');
            }
        });
    };
    
    // Initial check for elements in viewport
    animateOnScroll();
    
    // Listen for scroll events
    window.addEventListener('scroll', animateOnScroll);
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 80, // Offset for header
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Add pulsing effect to buttons
    const buttons = document.querySelectorAll('.btn, .register-btn, .join-class');
    buttons.forEach(button => {
        button.addEventListener('mouseover', function() {
            this.classList.add('pulse');
        });
        
        button.addEventListener('mouseout', function() {
            this.classList.remove('pulse');
        });
    });
    
    // Mobile navigation toggle (to be implemented if needed)
    const createMobileNav = function() {
        const header = document.querySelector('header');
        const nav = document.querySelector('nav');
        
        if (header && nav && window.innerWidth < 768) {
            const menuToggle = document.createElement('div');
            menuToggle.classList.add('menu-toggle');
            menuToggle.innerHTML = '<i class="fas fa-bars"></i>';
            
            menuToggle.addEventListener('click', function() {
                nav.classList.toggle('active');
                this.classList.toggle('active');
            });
            
            // Only add if it doesn't exist
            if (!document.querySelector('.menu-toggle')) {
                header.querySelector('.container').appendChild(menuToggle);
            }
        }
    };
    
    // Initialize mobile navigation
    createMobileNav();
    
    // Re-initialize on window resize
    window.addEventListener('resize', createMobileNav);
});

// Add parallax effect to hero section background
window.addEventListener('scroll', function() {
    const hero = document.querySelector('.hero');
    if (hero) {
        const scrollPosition = window.pageYOffset;
        hero.style.backgroundPosition = `center ${scrollPosition * 0.4}px`;
    }
});

// Form validation with visual feedback
if (document.getElementById('registerForm')) {
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        let valid = true;
        const inputs = this.querySelectorAll('input[required], select[required], textarea[required]');
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                valid = false;
                
                // Add visual feedback
                input.classList.add('error');
                
                // Remove error class when input changes
                input.addEventListener('input', function() {
                    this.classList.remove('error');
                }, { once: true });
            }
        });
        
        if (valid) {
            // Show success message with animation
            const successMessage = document.createElement('div');
            successMessage.classList.add('success-message');
            successMessage.textContent = 'Đăng ký thành công! Chúng tôi sẽ liên hệ với bạn sớm nhất có thể.';
            
            this.appendChild(successMessage);
            
            // Reset form after delay
            setTimeout(() => {
                this.reset();
                successMessage.remove();
            }, 3000);
        }
    });
} 