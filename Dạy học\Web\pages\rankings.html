<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bảng Xếp <PERSON> - <PERSON>thon</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .page-header {
            text-align: center;
            margin: 120px 0 50px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .leaderboard-container {
            max-width: 800px;
            margin: 0 auto 50px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .leaderboard-header {
            background-color: #111;
            color: white;
            padding: 15px 20px;
            display: grid;
            grid-template-columns: 80px 1fr 120px;
            align-items: center;
            font-weight: 500;
        }
        
        .leaderboard-item {
            padding: 20px;
            display: grid;
            grid-template-columns: 80px 1fr 120px;
            align-items: center;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
        }
        
        .leaderboard-item:hover {
            background-color: #f8f9fa;
        }
        
        .leaderboard-item:last-child {
            border-bottom: none;
        }
        
        .rank {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            text-align: center;
        }
        
        .rank-1 {
            color: #FFD700; /* Gold */
        }
        
        .rank-2 {
            color: #C0C0C0; /* Silver */
        }
        
        .rank-3 {
            color: #CD7F32; /* Bronze */
        }
        
        .student-info {
            display: flex;
            align-items: center;
        }
        
        .student-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 15px;
            border: 2px solid #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .student-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .student-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }
        
        .student-class {
            color: #666;
            font-size: 0.9rem;
        }
        
        .score {
            font-weight: 700;
            color: #4285F4;
            font-size: 1.2rem;
            text-align: center;
        }
        
        .leaderboard-item:nth-child(odd) {
            background-color: #f8f9fa;
        }
        
        .leaderboard-item:nth-child(odd):hover {
            background-color: #f1f3f5;
        }
        
        .rank-badge {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-weight: 700;
            color: white;
        }
        
        .rank-badge-1 {
            background-color: #FFD700;
            box-shadow: 0 3px 10px rgba(255, 215, 0, 0.3);
        }
        
        .rank-badge-2 {
            background-color: #C0C0C0;
            box-shadow: 0 3px 10px rgba(192, 192, 192, 0.3);
        }
        
        .rank-badge-3 {
            background-color: #CD7F32;
            box-shadow: 0 3px 10px rgba(205, 127, 50, 0.3);
        }
        
        .rank-badge-other {
            background-color: #4285F4;
            box-shadow: 0 3px 10px rgba(66, 133, 244, 0.3);
        }
        
        .last-updated {
            text-align: center;
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 50px;
            font-style: italic;
        }
        
        @media (max-width: 768px) {
            .leaderboard-header,
            .leaderboard-item {
                grid-template-columns: 60px 1fr 80px;
                padding: 15px 10px;
            }
            
            .student-avatar {
                width: 40px;
                height: 40px;
                margin-right: 10px;
            }
            
            .student-name {
                font-size: 0.9rem;
            }
            
            .student-class {
                font-size: 0.8rem;
            }
            
            .score {
                font-size: 1rem;
            }
            
            .rank {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="Vthon Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="classes.html">Lớp Học</a></li>
                    <li><a href="achievements.html">Thành Tích</a></li>
                    <li><a href="register.html">Đăng Ký</a></li>
                    <li><a href="rankings.html" class="active">Bảng Xếp Hạng</a></li>
                    <li><a href="research.html">Nghiên Cứu</a></li>
                    <li><a href="account.html">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Rankings Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Bảng Xếp Hạng Học Viên</h1>
                <p>Top 5 học viên có thành tích xuất sắc nhất dựa trên điểm tổng các bài kiểm tra</p>
            </div>
            
            <div class="leaderboard-container">
                <div class="leaderboard-header">
                    <div>Hạng</div>
                    <div>Học Viên</div>
                    <div>Điểm Tổng</div>
                </div>
                
                <!-- Rank 1 -->
                <div class="leaderboard-item">
                    <div class="rank">
                        <div class="rank-badge rank-badge-1">1</div>
                    </div>
                    <div class="student-info">
                        <div class="student-avatar">
                            <img src="../assets/images/user.png" alt="Học viên">
                        </div>
                        <div>
                            <div class="student-name">Nguyễn Văn A</div>
                            <div class="student-class">Python - A</div>
                        </div>
                    </div>
                    <div class="score">98</div>
                </div>
                
                <!-- Rank 2 -->
                <div class="leaderboard-item">
                    <div class="rank">
                        <div class="rank-badge rank-badge-2">2</div>
                    </div>
                    <div class="student-info">
                        <div class="student-avatar">
                            <img src="../assets/images/user.png" alt="Học viên">
                        </div>
                        <div>
                            <div class="student-name">Trần Thị B</div>
                            <div class="student-class">Python - A</div>
                        </div>
                    </div>
                    <div class="score">95</div>
                </div>
                
                <!-- Rank 3 -->
                <div class="leaderboard-item">
                    <div class="rank">
                        <div class="rank-badge rank-badge-3">3</div>
                    </div>
                    <div class="student-info">
                        <div class="student-avatar">
                            <img src="../assets/images/user.png" alt="Học viên">
                        </div>
                        <div>
                            <div class="student-name">Phạm Văn C</div>
                            <div class="student-class">Python - C</div>
                        </div>
                    </div>
                    <div class="score">92</div>
                </div>
                
                <!-- Rank 4 -->
                <div class="leaderboard-item">
                    <div class="rank">
                        <div class="rank-badge rank-badge-other">4</div>
                    </div>
                    <div class="student-info">
                        <div class="student-avatar">
                            <img src="../assets/images/user.png" alt="Học viên">
                        </div>
                        <div>
                            <div class="student-name">Lê Thị D</div>
                            <div class="student-class">Python - C</div>
                        </div>
                    </div>
                    <div class="score">90</div>
                </div>
                
                <!-- Rank 5 -->
                <div class="leaderboard-item">
                    <div class="rank">
                        <div class="rank-badge rank-badge-other">5</div>
                    </div>
                    <div class="student-info">
                        <div class="student-avatar">
                            <img src="../assets/images/user.png" alt="Học viên">
                        </div>
                        <div>
                            <div class="student-name">Hoàng Văn E</div>
                            <div class="student-class">Python - A</div>
                        </div>
                    </div>
                    <div class="score">88</div>
                </div>
            </div>
            
            <div class="last-updated">
                Cập nhật lần cuối: 01/11/2023
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; Kiến thức có đầu nhưng không có cuối - Vthon. Tất cả quyền được bảo lưu.</p>
        </div>
    </footer>

    <script src="../assets/js/script.js"></script>
</body>
</html> 